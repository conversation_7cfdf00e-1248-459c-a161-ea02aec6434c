package com.crrc.siom.ui.dashboard.detail

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import com.crrc.siom.ui.theme.SiomTheme
import androidx.compose.runtime.getValue
import androidx.lifecycle.ViewModelProvider
import com.crrc.common.Constant.USER_TYPE
import com.crrc.siom.data.SessionManager
import com.crrc.siom.service.WebSocketService
import com.crrc.siom.ui.dashboard.groupInfo.GroupInfoActivity
import com.crrc.siom.ui.dashboard.userinfo.UserInfoActivity

class ChatDetailActivity : ComponentActivity() {
    private var chatBinder: WebSocketService.ChatBinder? = null

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            chatBinder = service as? WebSocketService.ChatBinder
            viewModel.setChatBinder(chatBinder)
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            chatBinder = null
            viewModel.setChatBinder(null)
        }
    }

    private lateinit var viewModel: ChatDetailViewModel

    companion object {
        fun start(context: Context, userOrGroupId: String, userOrGroupName: String, chatType: Int) {
            context.startActivity(
                Intent(context, ChatDetailActivity::class.java).apply {
                    Log.d("chatType222", chatType.toString())
                    putExtra("userOrGroupId", userOrGroupId)
                    putExtra("userOrGroupName", userOrGroupName)
                    putExtra("chatType", chatType)
                }
            )
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel = ViewModelProvider(this)[ChatDetailViewModel::class.java]
        val intent2 = Intent(this, WebSocketService::class.java)
        bindService(intent2, serviceConnection, BIND_AUTO_CREATE)
        val userOrGroupId = intent.getStringExtra("userOrGroupId") ?: ""
        val userOrGroupName = intent.getStringExtra("userOrGroupName") ?: ""
        val chatType = intent.getIntExtra("chatType", 0)

        Log.d("userOrGroupId",userOrGroupId)
        Log.d("userOrGroupName",userOrGroupName)
        Log.d("chatType", chatType.toString())
        val myUserId = SessionManager().getUserId().toString()
        setContent {
            LaunchedEffect(userOrGroupId) {
                viewModel.getConversation(chatType, myUserId, userOrGroupId)
            }
            SiomTheme {
                Scaffold(
                    topBar = {
                        CenterAlignedTopAppBar(
                            navigationIcon = {
                                IconButton(onClick = { finish() }) {
                                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                                }
                            },
                            title = { Text(userOrGroupName) },
                            actions = {
                                IconButton(onClick = {
                                    if (chatType == USER_TYPE) {
                                        //用户点击显示用户信息
                                        UserInfoActivity.start(
                                            this@ChatDetailActivity,
                                            userOrGroupId
                                        )
                                    } else {
                                        //群聊显示群成员列表
                                        GroupInfoActivity.start(
                                            this@ChatDetailActivity,
                                            userOrGroupId
                                        )
                                    }
                                }) {
                                    Icon(Icons.Default.Person, contentDescription = "用户信息")
                                }
                            }
                        )
                    }
                ) { paddingValues ->
                    ChatDetailScreen(
                        modifier = Modifier.Companion.padding(paddingValues),
                        chatType = chatType,
                    )
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unbindService(serviceConnection)
    }
}